# Test info

- Name: Permission Boundary Integration Tests >> should handle concurrent permission changes across multiple sessions
- Location: /Users/<USER>/Developer/collaborative_content_editor_ko8hwk/src/__tests__/integration/permissions.integration.test.ts:221:3

# Error details

```
Error: expect(locator).toBeVisible()

Locator: locator('button:has-text("Sign out")')
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 15000ms
  - waiting for locator('button:has-text("Sign out")')

    at /Users/<USER>/Developer/collaborative_content_editor_ko8hwk/src/__tests__/integration/permissions.integration.test.ts:233:64
```

# Page snapshot

```yaml
- banner:
  - heading "Collaborative Editor" [level=2]
- main:
  - heading "Collaborative Editor" [level=1]
  - paragraph: Create and edit documents together in real-time
  - textbox "Email"
  - textbox "Password"
  - button "Sign in"
  - text: Don't have an account?
  - button "Sign up instead"
  - separator
  - text: or
  - separator
  - button "Sign in anonymously"
- region "Notifications alt+T"
```

# Test source

```ts
  133 |     // Wait for authentication
  134 |     await Promise.all([
  135 |       expect(adminPage.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 }),
  136 |       expect(editorPage.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 }),
  137 |       expect(viewerPage.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 })
  138 |     ]);
  139 |
  140 |     // Test permission consistency across all users
  141 |     // All anonymous users should see the Limited Access warning
  142 |     await expect(adminPage.locator('text=Limited Access')).toBeVisible({ timeout: 10000 });
  143 |     await expect(editorPage.locator('text=Limited Access')).toBeVisible({ timeout: 10000 });
  144 |     await expect(viewerPage.locator('text=Limited Access')).toBeVisible({ timeout: 10000 });
  145 |
  146 |     // Verify they all see the same restriction message
  147 |     const adminRestriction = await adminPage.locator('text=Anonymous users can only view shared documents').isVisible();
  148 |     const editorRestriction = await editorPage.locator('text=Anonymous users can only view shared documents').isVisible();
  149 |     const viewerRestriction = await viewerPage.locator('text=Anonymous users can only view shared documents').isVisible();
  150 |
  151 |     // At least one should be visible, but they might not all be visible at the same time due to timing
  152 |     const anyRestrictionVisible = adminRestriction || editorRestriction || viewerRestriction;
  153 |     expect(anyRestrictionVisible).toBe(true);
  154 |   });
  155 |
  156 |   test('should handle edge cases in permission inheritance', async () => {
  157 |     // Test complex permission inheritance scenarios
  158 |     
  159 |     // Sign in all users
  160 |     await Promise.all([
  161 |       adminPage.locator('button:has-text("Sign in anonymously")').click(),
  162 |       editorPage.locator('button:has-text("Sign in anonymously")').click(),
  163 |       viewerPage.locator('button:has-text("Sign in anonymously")').click()
  164 |     ]);
  165 |
  166 |     // Wait for authentication
  167 |     await Promise.all([
  168 |       expect(adminPage.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 }),
  169 |       expect(editorPage.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 }),
  170 |       expect(viewerPage.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 })
  171 |     ]);
  172 |
  173 |     // Test inheritance edge cases
  174 |     // All users should see consistent permission states
  175 |     const adminState = adminPage.locator('text=No documents yet').or(adminPage.locator('text=No Documents Available')).or(adminPage.locator('text=Limited Access')).first();
  176 |     const editorState = editorPage.locator('text=No documents yet').or(editorPage.locator('text=No Documents Available')).or(editorPage.locator('text=Limited Access')).first();
  177 |     const viewerState = viewerPage.locator('text=No documents yet').or(viewerPage.locator('text=No Documents Available')).or(viewerPage.locator('text=Limited Access')).first();
  178 |
  179 |     await expect(adminState).toBeVisible({ timeout: 10000 });
  180 |     await expect(editorState).toBeVisible({ timeout: 10000 });
  181 |     await expect(viewerState).toBeVisible({ timeout: 10000 });
  182 |
  183 |     // Verify no user has elevated permissions they shouldn't have
  184 |     await expect(adminPage.locator('button:has-text("New Document")')).not.toBeVisible();
  185 |     await expect(editorPage.locator('button:has-text("New Document")')).not.toBeVisible();
  186 |     await expect(viewerPage.locator('button:has-text("New Document")')).not.toBeVisible();
  187 |   });
  188 |
  189 |   test('should handle permission boundary violations gracefully', async () => {
  190 |     // Test what happens when users try to exceed their permissions
  191 |     
  192 |     // Sign in all users
  193 |     await Promise.all([
  194 |       adminPage.locator('button:has-text("Sign in anonymously")').click(),
  195 |       editorPage.locator('button:has-text("Sign in anonymously")').click(),
  196 |       viewerPage.locator('button:has-text("Sign in anonymously")').click()
  197 |     ]);
  198 |
  199 |     // Wait for authentication
  200 |     await Promise.all([
  201 |       expect(adminPage.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 }),
  202 |       expect(editorPage.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 }),
  203 |       expect(viewerPage.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 })
  204 |     ]);
  205 |
  206 |     // Test boundary violation handling
  207 |     // Users should see appropriate error messages or restrictions
  208 |     await expect(adminPage.locator('text=Limited Access')).toBeVisible();
  209 |     await expect(editorPage.locator('text=Limited Access')).toBeVisible();
  210 |     await expect(viewerPage.locator('text=Limited Access')).toBeVisible();
  211 |
  212 |     // Verify graceful degradation of functionality
  213 |     const adminHeader = await adminPage.locator('h2:has-text("Collaborative Editor")').textContent();
  214 |     const editorHeader = await editorPage.locator('h2:has-text("Collaborative Editor")').textContent();
  215 |     const viewerHeader = await viewerPage.locator('h2:has-text("Collaborative Editor")').textContent();
  216 |
  217 |     expect(adminHeader).toBe(editorHeader);
  218 |     expect(editorHeader).toBe(viewerHeader);
  219 |   });
  220 |
  221 |   test('should handle concurrent permission changes across multiple sessions', async () => {
  222 |     // Test permission changes affecting multiple active sessions
  223 |     
  224 |     // Sign in all users
  225 |     await Promise.all([
  226 |       adminPage.locator('button:has-text("Sign in anonymously")').click(),
  227 |       editorPage.locator('button:has-text("Sign in anonymously")').click(),
  228 |       viewerPage.locator('button:has-text("Sign in anonymously")').click()
  229 |     ]);
  230 |
  231 |     // Wait for authentication
  232 |     await Promise.all([
> 233 |       expect(adminPage.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 }),
      |                                                                ^ Error: expect(locator).toBeVisible()
  234 |       expect(editorPage.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 }),
  235 |       expect(viewerPage.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 })
  236 |     ]);
  237 |
  238 |     // Test concurrent session handling
  239 |     // All sessions should maintain consistent state
  240 |     const adminState = adminPage.locator('text=No documents yet').or(adminPage.locator('text=No Documents Available')).or(adminPage.locator('text=Limited Access')).first();
  241 |     const editorState = editorPage.locator('text=No documents yet').or(editorPage.locator('text=No Documents Available')).or(editorPage.locator('text=Limited Access')).first();
  242 |     const viewerState = viewerPage.locator('text=No documents yet').or(viewerPage.locator('text=No Documents Available')).or(viewerPage.locator('text=Limited Access')).first();
  243 |
  244 |     await expect(adminState).toBeVisible({ timeout: 10000 });
  245 |     await expect(editorState).toBeVisible({ timeout: 10000 });
  246 |     await expect(viewerState).toBeVisible({ timeout: 10000 });
  247 |
  248 |     // Verify all sessions show consistent permission restrictions
  249 |     await expect(adminPage.locator('text=Anonymous users can only view shared documents')).toBeVisible();
  250 |     await expect(editorPage.locator('text=Anonymous users can only view shared documents')).toBeVisible();
  251 |     await expect(viewerPage.locator('text=Anonymous users can only view shared documents')).toBeVisible();
  252 |   });
  253 |
  254 |   test('should handle permission caching and invalidation correctly', async () => {
  255 |     // Test permission caching behavior and cache invalidation
  256 |     
  257 |     // Sign in all users
  258 |     await Promise.all([
  259 |       adminPage.locator('button:has-text("Sign in anonymously")').click(),
  260 |       editorPage.locator('button:has-text("Sign in anonymously")').click()
  261 |     ]);
  262 |
  263 |     // Wait for authentication
  264 |     await Promise.all([
  265 |       expect(adminPage.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 }),
  266 |       expect(editorPage.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 })
  267 |     ]);
  268 |
  269 |     // Test cache behavior
  270 |     // Permissions should be consistent across page refreshes
  271 |     await adminPage.reload();
  272 |     await editorPage.reload();
  273 |
  274 |     // Wait for reload
  275 |     await Promise.all([
  276 |       adminPage.waitForLoadState('networkidle'),
  277 |       editorPage.waitForLoadState('networkidle')
  278 |     ]);
  279 |
  280 |     // Should still be authenticated after reload
  281 |     await expect(adminPage.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 });
  282 |     await expect(editorPage.locator('button:has-text("Sign out")')).toBeVisible({ timeout: 15000 });
  283 |
  284 |     // Should maintain same permission restrictions
  285 |     await expect(adminPage.locator('text=Limited Access')).toBeVisible();
  286 |     await expect(editorPage.locator('text=Limited Access')).toBeVisible();
  287 |   });
  288 | });
  289 |
```